<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JusBrasil Extrator - Ferramenta Web</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-gavel"></i>
                    <h1>JusBrasil Extrator</h1>
                </div>
                <p class="subtitle">Ferramenta moderna para extração de conteúdo jurídico</p>
            </div>
        </header>

        <main class="main-content">
            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-download"></i> Extrair Conteúdo</h2>
                    <p>Extraia conteúdo específico de páginas do JusBrasil</p>
                </div>

                <div class="form-section">
                    <div class="input-group">
                        <label for="urlInput">
                            <i class="fas fa-link"></i>
                            URL do JusBrasil
                        </label>
                        <input 
                            type="url" 
                            id="urlInput" 
                            placeholder="https://exemplo.jusbrasil.com.br/..."
                            required
                        >
                        <small class="help-text">Cole aqui a URL da página do JusBrasil que deseja extrair</small>
                    </div>

                    <div class="input-group">
                        <label for="extractionType">
                            <i class="fas fa-filter"></i>
                            Tipo de extração
                        </label>
                        <select id="extractionType">
                            <option value="inteiroTeor">Inteiro Teor</option>
                            <option value="custom">Texto específico</option>
                            <option value="full">Página completa</option>
                        </select>
                    </div>

                    <div id="customTextContainer" class="input-group" style="display: none;">
                        <label for="customText">
                            <i class="fas fa-search"></i>
                            Texto para buscar
                        </label>
                        <input 
                            type="text" 
                            id="customText" 
                            placeholder="Ex: Decisão, Sentença, Acórdão..."
                        >
                        <small class="help-text">Digite o texto específico que deseja encontrar na página</small>
                    </div>

                    <div class="button-group">
                        <button id="extractBtn" class="btn btn-primary">
                            <i class="fas fa-play"></i>
                            Extrair Conteúdo
                        </button>
                        <button id="downloadBtn" class="btn btn-secondary" disabled>
                            <i class="fas fa-download"></i>
                            Baixar HTML
                        </button>
                    </div>

                    <div id="status" class="status"></div>
                </div>
            </div>

            <div class="info-section">
                <div class="info-card">
                    <h3><i class="fas fa-info-circle"></i> Como usar</h3>
                    <ol>
                        <li>Cole a URL da página do JusBrasil</li>
                        <li>Escolha o tipo de extração desejado</li>
                        <li>Clique em "Extrair Conteúdo"</li>
                        <li>Baixe o arquivo HTML gerado</li>
                    </ol>
                </div>

                <div class="info-card">
                    <h3><i class="fas fa-shield-alt"></i> Recursos</h3>
                    <ul>
                        <li>Extração de "Inteiro Teor" automaticamente</li>
                        <li>Busca por texto específico</li>
                        <li>Download da página completa</li>
                        <li>Remoção de elementos desnecessários</li>
                        <li>Formatação limpa e organizada</li>
                    </ul>
                </div>
            </div>
        </main>

        <footer class="footer">
            <div class="footer-content">
                <p>
                    Desenvolvido com muito café por 
                    <a href="https://www.linkedin.com/in/ramos-souza/" target="_blank" rel="noopener noreferrer">
                        Ramos de Souza J
                    </a>
                </p>
                <div class="footer-links">
                    <a href="https://github.com" target="_blank" rel="noopener noreferrer">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://www.linkedin.com/in/ramos-souza/" target="_blank" rel="noopener noreferrer">
                        <i class="fab fa-linkedin"></i>
                    </a>
                </div>
            </div>
        </footer>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
