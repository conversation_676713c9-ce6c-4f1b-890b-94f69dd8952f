<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JusBrasil Extrator - Ferramenta Web</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        /* Reset e configurações base */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
            --background: #f8fafc;
            --surface: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --border-radius: 8px;
            --transition: all 0.2s ease-in-out;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header-content {
            background: var(--surface);
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
        }

        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .logo i {
            font-size: 2.5rem;
            color: var(--primary-color);
        }

        .logo h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .subtitle {
            font-size: 1.1rem;
            color: var(--text-secondary);
            font-weight: 400;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        @media (min-width: 1024px) {
            .main-content {
                grid-template-columns: 2fr 1fr;
            }
        }

        /* Cards */
        .card {
            background: var(--surface);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .card-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .card-header p {
            opacity: 0.9;
            font-size: 0.95rem;
        }

        /* Form Section */
        .form-section {
            padding: 2rem;
        }

        .input-group {
            margin-bottom: 1.5rem;
        }

        .input-group label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }

        .input-group input,
        .input-group select {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
            background: var(--surface);
        }

        .input-group input:focus,
        .input-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
        }

        .help-text {
            display: block;
            margin-top: 0.25rem;
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        /* Buttons */
        .button-group {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }

        .btn {
            flex: 1;
            padding: 0.875rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-secondary:hover:not(:disabled) {
            background: #475569;
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        /* Status Messages */
        .status {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: var(--border-radius);
            font-weight: 500;
            display: none;
        }

        .status.show {
            display: block;
        }

        .status.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .status.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .status.info {
            background: #dbeafe;
            color: #1d4ed8;
            border: 1px solid #bfdbfe;
        }

        /* Info Section */
        .info-section {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .info-card {
            background: var(--surface);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        .info-card h3 {
            color: var(--text-primary);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.1rem;
        }

        .info-card h3 i {
            color: var(--primary-color);
        }

        .info-card ol,
        .info-card ul {
            padding-left: 1.5rem;
        }

        .info-card li {
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
        }

        /* Footer */
        .footer {
            margin-top: 3rem;
            text-align: center;
        }

        .footer-content {
            background: var(--surface);
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }

        .footer-content p {
            color: var(--text-secondary);
            font-size: 0.95rem;
        }

        .footer-content a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }

        .footer-content a:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }

        .footer-links {
            display: flex;
            gap: 1rem;
        }

        .footer-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-links a:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .logo h1 {
                font-size: 2rem;
            }

            .button-group {
                flex-direction: column;
            }

            .footer-content {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-gavel"></i>
                    <h1>JusBrasil Extrator</h1>
                </div>
                <p class="subtitle">Ferramenta movida à café para extração de conteúdo jurídico</p>
            </div>
        </header>

        <main class="main-content">
            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-download"></i> Extrair Conteúdo</h2>
                    <p>Extraia conteúdo específico de páginas do JusBrasil</p>
                </div>

                <div class="form-section">
                    <div class="input-group">
                        <label for="urlInput">
                            <i class="fas fa-link"></i>
                            URL do JusBrasil
                        </label>
                        <input 
                            type="url" 
                            id="urlInput" 
                            placeholder="https://exemplo.jusbrasil.com.br/..."
                            required
                        >
                        <small class="help-text">Cole aqui a URL da página do JusBrasil que deseja extrair</small>
                    </div>

                    <div class="input-group">
                        <label for="extractionType">
                            <i class="fas fa-filter"></i>
                            Tipo de extração
                        </label>
                        <select id="extractionType">
                            <option value="inteiroTeor">Inteiro Teor</option>
                            <option value="custom">Texto específico</option>
                            <option value="full">Página completa</option>
                        </select>
                    </div>

                    <div id="customTextContainer" class="input-group" style="display: none;">
                        <label for="customText">
                            <i class="fas fa-search"></i>
                            Texto para buscar
                        </label>
                        <input 
                            type="text" 
                            id="customText" 
                            placeholder="Ex: Decisão, Sentença, Acórdão..."
                        >
                        <small class="help-text">Digite o texto específico que deseja encontrar na página</small>
                    </div>

                    <div class="button-group">
                        <button id="extractBtn" class="btn btn-primary">
                            <i class="fas fa-play"></i>
                            Extrair Conteúdo
                        </button>
                        <button id="downloadBtn" class="btn btn-secondary" disabled>
                            <i class="fas fa-download"></i>
                            Baixar HTML
                        </button>
                    </div>

                    <div id="status" class="status"></div>
                </div>
            </div>

            <div class="info-section">
                <div class="info-card">
                    <h3><i class="fas fa-info-circle"></i> Como usar</h3>
                    <ol>
                        <li>Cole a URL da página do JusBrasil</li>
                        <li>Escolha o tipo de extração desejado</li>
                        <li>Clique em "Extrair Conteúdo"</li>
                        <li>Baixe o arquivo HTML gerado</li>
                    </ol>
                </div>

                <div class="info-card">
                    <h3><i class="fas fa-shield-alt"></i> Recursos</h3>
                    <ul>
                        <li>Extração de "Inteiro Teor" automaticamente</li>
                        <li>Busca por texto específico</li>
                        <li>Download da página completa</li>
                        <li>Remoção de elementos desnecessários</li>
                        <li>Formatação limpa e organizada</li>
                    </ul>
                </div>
            </div>
        </main>

        <footer class="footer">
            <div class="footer-content">
                <p>
                    Desenvolvido com muito café por
                    <a href="https://www.linkedin.com/in/ramos-souza/" target="_blank" rel="noopener noreferrer">
                        Ramos de Souza J
                    </a>
                </p>
                <div class="footer-links">
                    <a href="https://github.com/RamosJSouza/" target="_blank" rel="noopener noreferrer">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://www.linkedin.com/in/ramos-souza/" target="_blank" rel="noopener noreferrer">
                        <i class="fab fa-linkedin"></i>
                    </a>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Classe principal para controlar a UI da aplicação web
        class JusBrasilExtractor {
            constructor() {
                this.initializeElements();
                this.initializeEventListeners();
                this.extractedHTML = '';
            }

            initializeElements() {
                this.elements = {
                    urlInput: document.getElementById('urlInput'),
                    extractionType: document.getElementById('extractionType'),
                    customTextContainer: document.getElementById('customTextContainer'),
                    customText: document.getElementById('customText'),
                    extractBtn: document.getElementById('extractBtn'),
                    downloadBtn: document.getElementById('downloadBtn'),
                    status: document.getElementById('status')
                };

                // Inicialmente desabilita o botão de download
                this.elements.downloadBtn.disabled = true;
            }

            initializeEventListeners() {
                this.elements.extractionType.addEventListener('change', () => this.handleExtractionTypeChange());
                this.elements.extractBtn.addEventListener('click', () => this.handleExtraction());
                this.elements.downloadBtn.addEventListener('click', () => this.handleDownload());
            }

            handleExtractionTypeChange() {
                const isCustom = this.elements.extractionType.value === 'custom';
                this.elements.customTextContainer.style.display = isCustom ? 'block' : 'none';

                if (isCustom) {
                    this.elements.customText.focus();
                }
            }

            async handleExtraction() {
                const url = this.elements.urlInput.value.trim();
                const extractionType = this.elements.extractionType.value;
                const customText = this.elements.customText.value;

                // Validações
                if (!url) {
                    this.showStatus('Por favor, insira uma URL válida', 'error');
                    return;
                }

                if (!url.includes('jusbrasil.com.br')) {
                    this.showStatus('Esta ferramenta funciona apenas com URLs do JusBrasil', 'error');
                    return;
                }

                if (extractionType === 'custom' && !customText.trim()) {
                    this.showStatus('Por favor, insira um texto para buscar', 'error');
                    return;
                }

                try {
                    this.elements.extractBtn.disabled = true;
                    this.showStatus('Processando URL... Esta é uma demonstração da funcionalidade.', 'info');

                    // Simula o processamento (em uma implementação real, seria necessário um backend)
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // Gera HTML de exemplo baseado nos parâmetros
                    this.extractedHTML = this.generateSampleHTML(url, extractionType, customText);
                    this.elements.downloadBtn.disabled = false;
                    this.showStatus('Conteúdo processado com sucesso! Clique em "Baixar HTML" para salvar.', 'success');

                } catch (error) {
                    console.error('Erro:', error);
                    this.showStatus(error.message, 'error');
                    this.elements.downloadBtn.disabled = true;
                } finally {
                    this.elements.extractBtn.disabled = false;
                }
            }

            generateSampleHTML(url, extractionType, customText = '') {
                const date = new Date().toLocaleString('pt-BR');
                let title = '';
                let content = '';

                switch(extractionType) {
                    case 'inteiroTeor':
                        title = 'Inteiro Teor';
                        content = `
                            <h2>INTEIRO TEOR</h2>
                            <div class="content-section">
                                <p><strong>TRIBUNAL:</strong> Tribunal de Justiça do Estado de São Paulo</p>
                                <p><strong>PROCESSO:</strong> 1234567-89.2023.8.26.0001</p>
                                <p><strong>RELATOR:</strong> Des. João da Silva</p>
                                <p><strong>JULGAMENTO:</strong> ${new Date().toLocaleDateString('pt-BR')}</p>

                                <h3>EMENTA</h3>
                                <p>Este é um exemplo de conteúdo extraído do JusBrasil. Em uma implementação real,
                                este seria o conteúdo completo do documento jurídico solicitado.</p>

                                <h3>ACÓRDÃO</h3>
                                <p>Vistos, relatados e discutidos estes autos...</p>
                                <p>ACORDAM os Desembargadores da Câmara de Direito Privado do Tribunal de Justiça
                                do Estado de São Paulo, por unanimidade de votos...</p>
                            </div>
                        `;
                        break;
                    case 'custom':
                        title = `Conteúdo: "${customText}"`;
                        content = `
                            <h2>BUSCA POR: "${customText.toUpperCase()}"</h2>
                            <div class="content-section">
                                <p>Resultado da busca pelo termo "<strong>${customText}</strong>" na página do JusBrasil.</p>
                                <div class="search-result">
                                    <h3>Seção encontrada</h3>
                                    <p>Este é um exemplo de conteúdo que contém o termo "${customText}"
                                    que foi localizado na página original.</p>
                                    <p>Em uma implementação real, este seria o conteúdo específico
                                    extraído da página do JusBrasil baseado no termo de busca fornecido.</p>
                                </div>
                            </div>
                        `;
                        break;
                    case 'full':
                        title = 'Página Completa';
                        content = `
                            <h2>PÁGINA COMPLETA</h2>
                            <div class="content-section">
                                <p>Este é um exemplo da extração completa da página do JusBrasil.</p>
                                <p>Incluiria todo o conteúdo da página, limpo de elementos desnecessários
                                como anúncios, menus de navegação e scripts.</p>

                                <h3>Conteúdo Principal</h3>
                                <p>Aqui estaria todo o texto principal da página, formatado de forma limpa e organizada.</p>

                                <h3>Metadados</h3>
                                <ul>
                                    <li>Data de publicação</li>
                                    <li>Autor/Tribunal</li>
                                    <li>Número do processo</li>
                                    <li>Outras informações relevantes</li>
                                </ul>
                            </div>
                        `;
                        break;
                }

                return `<!DOCTYPE html>
                <html lang="pt-BR">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>${title} - JusBrasil Extrator</title>
                    <style>
                        body {
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            line-height: 1.6;
                            margin: 0;
                            padding: 20px;
                            max-width: 1000px;
                            margin: 0 auto;
                            background: #f8f9fa;
                        }
                        .container {
                            background: white;
                            padding: 30px;
                            border-radius: 8px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }
                        .header {
                            margin-bottom: 30px;
                            padding-bottom: 20px;
                            border-bottom: 2px solid #e9ecef;
                        }
                        .header h1 {
                            color: #2c3e50;
                            margin-bottom: 10px;
                        }
                        .metadata {
                            background: #f8f9fa;
                            padding: 15px;
                            border-radius: 5px;
                            margin-bottom: 20px;
                        }
                        .content-section {
                            margin-top: 20px;
                        }
                        .content-section h2 {
                            color: #2c3e50;
                            border-bottom: 1px solid #dee2e6;
                            padding-bottom: 10px;
                        }
                        .content-section h3 {
                            color: #495057;
                            margin-top: 25px;
                        }
                        .search-result {
                            background: #e3f2fd;
                            padding: 15px;
                            border-left: 4px solid #2196f3;
                            margin: 15px 0;
                        }
                        .footer {
                            margin-top: 40px;
                            padding-top: 20px;
                            border-top: 1px solid #dee2e6;
                            font-size: 0.9em;
                            color: #6c757d;
                            text-align: center;
                        }
                        .footer a {
                            color: #007bff;
                            text-decoration: none;
                        }
                        .footer a:hover {
                            text-decoration: underline;
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1>${title}</h1>
                            <div class="metadata">
                                <p><strong>URL Original:</strong> <a href="${url}" target="_blank">${url}</a></p>
                                <p><strong>Extraído em:</strong> ${date}</p>
                                <p><strong>Tipo de extração:</strong> ${this.getExtractionTypeLabel(extractionType)}</p>
                            </div>
                        </div>

                        <div class="content">
                            ${content}
                        </div>

                        <div class="footer">
                            <p>Documento gerado pelo <strong>JusBrasil Extrator</strong></p>
                            <p>Desenvolvido com muito café por
                                <a href="https://www.linkedin.com/in/ramos-souza/" target="_blank">Ramos de Souza J</a>
                            </p>
                        </div>
                    </div>
                </body>
                </html>`;
            }

            getExtractionTypeLabel(type) {
                const labels = {
                    'inteiroTeor': 'Inteiro Teor',
                    'custom': 'Texto Específico',
                    'full': 'Página Completa'
                };
                return labels[type] || type;
            }

            handleDownload() {
                if (!this.extractedHTML) {
                    this.showStatus('Nenhum conteúdo para baixar', 'error');
                    return;
                }

                try {
                    const filename = `jusbrasil_extracao_${new Date().toISOString().slice(0, 10)}.html`;
                    this.downloadFile(this.extractedHTML, filename, 'text/html');
                    this.showStatus('Arquivo baixado com sucesso!', 'success');
                } catch (error) {
                    console.error('Erro ao baixar:', error);
                    this.showStatus('Erro ao baixar o arquivo', 'error');
                }
            }

            downloadFile(content, filename, type) {
                const blob = new Blob([content], { type });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');

                try {
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                } finally {
                    setTimeout(() => URL.revokeObjectURL(url), 100);
                }
            }

            showStatus(message, type) {
                this.elements.status.textContent = message;
                this.elements.status.className = `status ${type} show`;

                // Auto-hide success messages after 5 seconds
                if (type === 'success') {
                    setTimeout(() => {
                        this.elements.status.classList.remove('show');
                    }, 5000);
                }
            }
        }

        // Inicializa a aplicação quando o DOM estiver carregado
        document.addEventListener('DOMContentLoaded', () => {
            new JusBrasilExtractor();
        });
    </script>
</body>
</html>
