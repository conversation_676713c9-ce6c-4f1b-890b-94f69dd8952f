body {
    font-family: Arial, sans-serif;
    width: 300px;
    padding: 15px;
    margin: 0;
}

h1 {
    font-size: 18px;
    margin-bottom: 15px;
    color: #2c3e50;
}

select, button {
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    border-radius: 4px;
}

select {
    border: 1px solid #ddd;
    background-color: white;
}

button {
    background-color: #3498db;
    color: white;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

button:hover:not(:disabled) {
    background-color: #2980b9;
}

button:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
    opacity: 0.7;
}

#customTextContainer {
    display: none;
    margin-bottom: 10px;
}

input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

input:focus {
    border-color: #3498db;
    outline: none;
}

.status {
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
    font-size: 14px;
}

.status.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}
