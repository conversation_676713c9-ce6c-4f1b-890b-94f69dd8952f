/* Reset e configurações base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --background: #f8fafc;
    --surface: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --transition: all 0.2s ease-in-out;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 3rem;
}

.header-content {
    background: var(--surface);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.logo i {
    font-size: 2.5rem;
    color: var(--primary-color);
}

.logo h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    font-weight: 400;
}

/* Main Content */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
}

@media (min-width: 1024px) {
    .main-content {
        grid-template-columns: 2fr 1fr;
    }
}

/* Cards */
.card {
    background: var(--surface);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    padding: 2rem;
    text-align: center;
}

.card-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.card-header p {
    opacity: 0.9;
    font-size: 0.95rem;
}

/* Form Section */
.form-section {
    padding: 2rem;
}

.input-group {
    margin-bottom: 1.5rem;
}

.input-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--surface);
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.help-text {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

/* Buttons */
.button-group {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.btn {
    flex: 1;
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #475569;
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Status Messages */
.status {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    display: none;
}

.status.show {
    display: block;
}

.status.success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.status.error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.status.info {
    background: #dbeafe;
    color: #1d4ed8;
    border: 1px solid #bfdbfe;
}

/* Info Section */
.info-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.info-card {
    background: var(--surface);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.info-card h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
}

.info-card h3 i {
    color: var(--primary-color);
}

.info-card ol,
.info-card ul {
    padding-left: 1.5rem;
}

.info-card li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

/* Footer */
.footer {
    margin-top: 3rem;
    text-align: center;
}

.footer-content {
    background: var(--surface);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.footer-content p {
    color: var(--text-secondary);
    font-size: 0.95rem;
}

.footer-content a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.footer-content a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

.footer-links {
    display: flex;
    gap: 1rem;
}

.footer-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .logo h1 {
        font-size: 2rem;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .footer-content {
        padding: 1.5rem;
    }
}
